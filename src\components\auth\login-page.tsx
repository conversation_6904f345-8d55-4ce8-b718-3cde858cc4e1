"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { LoginButton } from "./login-button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../ui/card";
import { Loader2 } from "lucide-react";

export function LoginPage() {
  const { status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "authenticated") {
      router.push("/");
    }
  }, [status, router]);

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
          <p className="text-sm font-medium text-gray-600">Carregando...</p>
        </div>
      </div>
    );
  }

  if (status === "authenticated") {
    return null;
  }

  return (
    <div className="w-screen h-screen grid grid-cols-1 md:grid-cols-2 bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Left Section - Welcome Content */}
      <div className="relative overflow-hidden flex items-center justify-center p-6 md:p-8 lg:p-12 xl:p-16 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/90 via-purple-600/90 to-indigo-700/90"></div>

        {/* Main content */}
        <div className="relative z-10 lg:max-w-2xl flex-1 text-white">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 leading-tight">
            Bem-vindo ao Gatewayfy
          </h1>
          <p className="text-base sm:text-lg md:text-xl lg:text-2xl opacity-90 leading-relaxed">
            Sua plataforma completa para gerenciamento de pagamentos e vendas online.
            Conecte-se e transforme seu negócio digital.
          </p>

          {/* Feature highlights */}
          <div className="mt-8 space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm md:text-base opacity-80">Pagamentos seguros e rápidos</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm md:text-base opacity-80">Dashboard completo de vendas</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span className="text-sm md:text-base opacity-80">Integração simples e eficiente</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Section - Login Card */}
      <div className="flex-1 bg-gray-50 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-12 xl:p-16 min-h-[50vh] lg:min-h-screen">
        <div className="max-w-sm sm:max-w-md w-full">
          <Card className="shadow-2xl border-0 bg-white rounded-2xl">
            <CardHeader className="space-y-4 sm:space-y-6 pb-6 sm:pb-8 text-center px-4 sm:px-6 pt-6 sm:pt-8">
              <div className="space-y-2">
                <CardTitle className="text-xl sm:text-2xl font-bold text-gray-900">
                  Fazer Login
                </CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 leading-relaxed">
                  Entre com sua conta Google para acessar a plataforma
                </CardDescription>
              </div>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6 pb-6 sm:pb-8 px-4 sm:px-6">
              <LoginButton />
              <div className="text-center">
                <p className="text-xs sm:text-sm text-gray-500 leading-relaxed">
                  Ao continuar, você concorda com nossos{" "}
                  <a href="#" className="text-blue-600 hover:text-blue-700 underline transition-colors">
                    Termos de Serviço
                  </a>{" "}
                  e{" "}
                  <a href="#" className="text-blue-600 hover:text-blue-700 underline transition-colors">
                    Política de Privacidade
                  </a>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
