// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  name      String
  email     String   @unique
  password  String
  googleId  String?  @unique
  createdAt DateTime @default(now())

  products Product[]
  orders   Order[]
}

model Product {
  id          String   @id @default(uuid())
  name        String
  description String
  price       Decimal
  imageUrl    String
  createdAt   DateTime @default(now())

  userId    String
  user      User        @relation(fields: [userId], references: [id])
  OrderItem OrderItem[]
}

model Order {
  id         String   @id @default(uuid())
  buyerName  String
  buyerEmail String
  buyerPhone String
  total      Decimal
  paid       Boolean  @default(false)
  createdAt  DateTime @default(now())

  userId String
  user   User   @relation(fields: [userId], references: [id])

  orderItems OrderItem[]
}

model OrderItem {
  id        String @id @default(uuid())
  quantity  Int
  productId String
  orderId   String

  product Product @relation(fields: [productId], references: [id])
  order   Order   @relation(fields: [orderId], references: [id])
}
