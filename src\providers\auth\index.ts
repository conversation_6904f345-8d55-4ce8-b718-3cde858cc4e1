import { prisma } from "../../lib/prisma";
import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID!,
      clientSecret: process.env.AUTH_GOOGLE_SECRET!,
    }),
  ],
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async signIn({ user, account }) {
      try {
        const existingUser = await prisma.user.findUnique({
          where: { email: user.email! },
        });

        if (!existingUser) {
          await prisma.user.create({
            data: {
              email: user.email!,
              name: user.name!,
              password: "", // Required by schema but not used with Google auth
              googleId: account?.providerAccountId,
            },
          });
        }

        return true;
      } catch (error) {
        console.error(error);
        return false;
      }
    },
    async session({ session }) {
      if (session.user?.email) {
        const existingUser = await prisma.user.findUnique({
          where: { email: session.user.email },
        });

        // Add user data to session
        if (existingUser) {
          session.user = {
            ...session.user,
            id: existingUser.id,
          };
        }
      }

      return session;
    },
  },
});
