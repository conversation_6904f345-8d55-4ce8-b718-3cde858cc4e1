"use client";

import { useSession } from "next-auth/react";
import { LoginButton, UserInfo } from "../../../components/auth";

export default function HomePage() {
  const { data: session, status } = useSession();

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <div className="text-center sm:text-left">
          <h1 className="text-4xl font-bold mb-4">Welcome to Gatewayfy</h1>
          <p className="text-xl text-gray-600">Your gateway to seamless payments</p>
        </div>

        {status === "loading" && (
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        )}

        {status === "authenticated" && (
          <div className="flex flex-col gap-4 items-center">
            <UserInfo />
            <div className="flex gap-4 items-center flex-col sm:flex-row">
              <a
                className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto"
                href="/dashboard"
              >
                Go to Dashboard
              </a>
              <a
                className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
                href="/dashboard/products"
              >
                View Products
              </a>
            </div>
          </div>
        )}

        {status === "unauthenticated" && (
          <div className="flex flex-col gap-4 items-center">
            <p className="text-gray-600">Faça login para acessar o dashboard</p>
            <div className="w-64">
              <LoginButton />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
