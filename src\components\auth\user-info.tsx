"use client";

import { useSession } from "next-auth/react";
import Image from "next/image";
import { LogoutButton } from "./logout-button";

export function UserInfo() {
  const { data: session } = useSession();

  if (!session || !session.user) {
    return null;
  }

  return (
    <div className="flex items-center gap-4">
      {session.user.image && (
        <div className="relative h-8 w-8 rounded-full overflow-hidden">
          <Image
            src={session.user.image}
            alt={session.user.name || "Avatar do usuário"}
            fill
            className="object-cover"
          />
        </div>
      )}
      <div>
        <p className="text-sm font-medium">{session.user.name}</p>
        <p className="text-xs text-gray-500">{session.user.email}</p>
      </div>
      <LogoutButton />
    </div>
  );
}