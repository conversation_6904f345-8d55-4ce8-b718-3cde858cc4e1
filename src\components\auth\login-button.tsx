"use client";

import { signIn } from "next-auth/react";
import Button from "@/components/ui/button";
import Image from "next/image";
import { cn } from "@/src/lib/utils";
import GoogleIcon from "@/public/logo-google.png"

export function LoginButton() {
  return (
    <div>
      <div className={cn("grid gap-6")}>
        <Button
          className="h-[60px] cursor-pointer px-6 "
          type="button"
          variant="secondary"
          onClick={() => signIn("google", { callbackUrl: "/" })}
        >
          <Image alt="Google" width={20} height={20} src={GoogleIcon} />
          <div>Login com Google</div>
        </Button>
      </div>
    </div>
  );
}
