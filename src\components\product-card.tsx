interface Product {
  id: string;
  name: string;
  price: number;
  description: string;
  imageUrl?: string;
}

interface ProductCardProps {
  product: Product;
  onBuyClick?: (productId: string) => void;
}

export default function ProductCard({ product, onBuyClick }: ProductCardProps) {
  return (
    <div className="border rounded-lg p-4 shadow-md hover:shadow-lg transition-shadow">
      {product.imageUrl && (
        <img
          src={product.imageUrl}
          alt={product.name}
          className="w-full h-48 object-cover rounded-md mb-4"
        />
      )}
      
      <h3 className="text-lg font-semibold mb-2">{product.name}</h3>
      <p className="text-gray-600 mb-3">{product.description}</p>
      
      <div className="flex justify-between items-center">
        <span className="text-xl font-bold text-green-600">
          ${product.price.toFixed(2)}
        </span>
        
        <button
          onClick={() => onBuyClick?.(product.id)}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
        >
          Buy Now
        </button>
      </div>
    </div>
  );
}
