import type { Metadata, Viewport } from "next";
import { Mont<PERSON><PERSON> } from "next/font/google";
import { Suspense } from "react";
import Loading from "../components/ui/app-loading";
import { NextAuthProvider } from "../components/auth/auth-provider";
import "./globals.css";

const fontSans = Montserrat({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
};

export const metadata: Metadata = {
  title: "Gatewayfy",
  description: "Just Do It"
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${fontSans.variable} custom-scrollbar`}
      >
        <NextAuthProvider>
          <Suspense fallback={<Loading />}>{children}</Suspense>
        </NextAuthProvider>
      </body>
    </html>
  );
}
